#!/usr/bin/env node

import readline from 'readline';
import fs from 'fs';
import path from 'path';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setup() {
  console.log('🚀 مرحباً بك في إعداد NUMINF');
  console.log('=====================================\n');

  console.log('📱 للحصول على API ID و API Hash:');
  console.log('1. اذهب إلى https://my.telegram.org');
  console.log('2. سجل دخول برقم هاتفك');
  console.log('3. اذهب إلى "API Development Tools"');
  console.log('4. أنشئ تطبيق جديد\n');

  const apiId = await question('أدخل API ID: ');
  const apiHash = await question('أدخل API Hash: ');
  const phoneNumber = await question('أدخل رقم هاتفك (مع رمز الدولة، مثل: +************): ');

  const envContent = `# Server Configuration
PORT=5000
NODE_ENV=development

# Telegram User Account Configuration
TELEGRAM_API_ID=${apiId}
TELEGRAM_API_HASH=${apiHash}
TELEGRAM_PHONE_NUMBER=${phoneNumber}
TELEGRAM_SESSION=

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173
`;

  fs.writeFileSync('.env', envContent);
  
  console.log('\n✅ تم إنشاء ملف .env بنجاح!');
  console.log('\n📋 الخطوات التالية:');
  console.log('1. تشغيل: npm install');
  console.log('2. تشغيل: npm run server');
  console.log('3. في terminal آخر: npm run dev');
  console.log('\n⚠️  ملاحظة: في أول تشغيل ستحتاج لإدخال كود التحقق من تليجرام');
  
  rl.close();
}

setup().catch(console.error);
