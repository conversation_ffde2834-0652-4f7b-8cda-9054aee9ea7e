# 🔍 كيف يعمل NUMINF

## آلية العمل

### 1. 👤 حسابك الشخصي
- النظام يستخدم **حسابك الشخصي** في تليجرام
- **ليس بوت** - هو حسابك العادي
- تسجل دخول مرة واحدة فقط

### 2. 🔄 تدفق العمل

```
المستخدم يدخل رقم → موقعك → حسابك في تليجرام → @TrueCaller_Z_Bot → النتيجة ترجع للمستخدم
```

#### التفصيل:
1. **المستخدم** يدخل رقم هاتف في موقعك
2. **موقعك** يرسل الرقم لخادمك
3. **خادمك** يستخدم حسابك في تليجرام
4. **حسابك** يرسل الرقم لـ @TrueCaller_Z_Bot
5. **البوت** يرد بمعلومات الرقم
6. **النتيجة** ترجع للمستخدم في موقعك

### 3. 🔐 الأمان
- **بياناتك آمنة**: لا تُشارك مع أحد
- **Session محفوظ**: تسجل دخول مرة واحدة فقط
- **لا توكينات خارجية**: كل شيء تحت سيطرتك

### 4. 📱 المتطلبات
- حساب تليجرام شخصي
- محادثة مع @TrueCaller_Z_Bot
- API credentials من my.telegram.org

## خطوات الإعداد

### الخطوة 1: الحصول على API
1. اذهب إلى https://my.telegram.org
2. سجل دخول برقم هاتفك
3. اذهب إلى "API Development Tools"
4. أنشئ تطبيق واحصل على API ID و API Hash

### الخطوة 2: إعداد المشروع
```bash
npm install
npm run setup
```

### الخطوة 3: تسجيل الدخول
```bash
npm run login
```
- سيطلب كود التحقق من تليجرام
- سيحفظ session للاستخدام المستقبلي

### الخطوة 4: التأكد من البوت
- ابحث عن @TrueCaller_Z_Bot في تليجرام
- ابدأ محادثة معه
- جرب إرسال رقم للتأكد من عمله

### الخطوة 5: التشغيل
```bash
npm run dev:full
```

## الاستكشاف والإصلاح

### مشكلة: "غير مسجل دخول"
**الحل:** شغل `npm run login`

### مشكلة: "فشل في التواصل مع البوت"
**الحل:** 
- تأكد من وجود محادثة مع @TrueCaller_Z_Bot
- جرب إرسال رقم يدوياً للبوت أولاً

### مشكلة: "API credentials خاطئة"
**الحل:**
- تأكد من صحة API ID و API Hash
- شغل `npm run setup` مرة أخرى

## الميزات

### ✅ ما يعمل
- البحث عن أرقام الهواتف
- عرض النتائج بشكل جميل
- واجهة عربية ريسبونسف
- حفظ session تلقائياً

### 🔄 للتطوير المستقبلي
- إضافة قاعدة بيانات للنتائج
- نظام cache للبحثات السابقة
- إضافة المزيد من مصادر البحث
- نظام إحصائيات

## الدعم الفني
إذا واجهت مشاكل:
1. تأكد من اتصال الإنترنت
2. تأكد من صحة بيانات API
3. تأكد من وجود محادثة مع البوت
4. شغل `npm run login` مرة أخرى
