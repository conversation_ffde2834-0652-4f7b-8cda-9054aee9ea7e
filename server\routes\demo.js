import express from 'express';

const router = express.Router();

// Demo endpoint for testing without real TrueCaller bot
router.post('/demo-lookup', async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({
        error: 'Phone number is required',
        success: false
      });
    }

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return demo data
    const demoResult = {
      name: 'أحمد محمد',
      location: 'القاهرة, مصر',
      carrier: 'فودافون مصر',
      type: 'موبايل',
      phoneNumber: phoneNumber,
      rawText: `معلومات الرقم: ${phoneNumber}
الاسم: أحمد محمد
الموقع: القاهرة, مصر
شركة الاتصالات: فودافون مصر
نوع الخط: موبايل
تم التحقق: نعم`
    };

    res.json({
      success: true,
      data: demoResult,
      message: 'Demo lookup successful'
    });

  } catch (error) {
    console.error('Demo lookup error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to process demo request'
    });
  }
});

export default router;
