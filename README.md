# NUMINF - Number Information Lookup

موقع للحصول على معلومات أرقام الهواتف باستخدام خدمة TrueCaller Bot.

## المميزات

- 🔍 البحث السريع عن معلومات أرقام الهواتف
- 📱 واجهة مستخدم ريسبونسف وجذابة
- ⚡ تأثيرات وانيميشن متقدمة
- 🔒 آمن وموثوق
- 🌐 يدعم اللغة العربية

## التقنيات المستخدمة

### Frontend
- React 19
- Tailwind CSS
- Vite
- Modern CSS Animations

### Backend
- Node.js
- Express.js
- Axios للتواصل مع Telegram API
- CORS للأمان

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. إعد<PERSON> متغيرات البيئة
انسخ ملف `.env.example` إلى `.env` وأضف البيانات المطلوبة:

```bash
cp .env.example .env
```

ثم عدل الملف وأضف:
- `TELEGRAM_BOT_TOKEN`: توكن البوت من BotFather
- `TELEGRAM_CHAT_ID`: معرف المحادثة مع البوت

### 3. تشغيل المشروع

#### تشغيل الفرونت إند فقط:
```bash
npm run dev
```

#### تشغيل الباك إند فقط:
```bash
npm run server
```

#### تشغيل المشروع كاملاً:
```bash
npm run dev:full
```

## كيفية الحصول على بيانات Telegram Bot

### 1. إنشاء البوت
1. ابحث عن `@BotFather` في Telegram
2. أرسل `/newbot`
3. اتبع التعليمات للحصول على التوكن

### 2. الحصول على Chat ID
1. ابدأ محادثة مع `@TrueCaller_Z_Bot`
2. أرسل أي رسالة
3. استخدم هذا الرابط لمعرفة Chat ID:
   `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`

## البناء للإنتاج

```bash
npm run build
```

## الهيكل

```
NUMINF/
├── src/
│   ├── components/
│   │   ├── PhoneInput.jsx
│   │   ├── ResultDisplay.jsx
│   │   └── LoadingSpinner.jsx
│   ├── App.jsx
│   ├── App.css
│   └── main.jsx
├── server/
│   ├── routes/
│   │   └── api.js
│   ├── services/
│   │   └── truecaller.js
│   └── server.js
├── public/
└── package.json
```

## المساهمة

نرحب بالمساهمات! يرجى فتح Issue أو Pull Request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
