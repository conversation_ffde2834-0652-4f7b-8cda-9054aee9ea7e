# NUMINF - Number Information Lookup

موقع للحصول على معلومات أرقام الهواتف باستخدام خدمة TrueCaller Bot.

## المميزات

- 🔍 البحث السريع عن معلومات أرقام الهواتف
- 📱 واجهة مستخدم ريسبونسف وجذابة
- ⚡ تأثيرات وانيميشن متقدمة
- 🔒 آمن وموثوق
- 🌐 يدعم اللغة العربية

## التقنيات المستخدمة

### Frontend
- React 19
- Tailwind CSS
- Vite
- Modern CSS Animations

### Backend
- Node.js
- Express.js
- Axios للتواصل مع Telegram API
- CORS للأمان

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. الإعداد السريع
استخدم أداة الإعداد التفاعلية:

```bash
npm run setup
```

أو يمكنك إعداد متغيرات البيئة يدوياً:
```bash
cp .env.example .env
```

ثم عدل الملف وأضف:
- `TELEGRAM_API_ID`: من my.telegram.org
- `TELEGRAM_API_HASH`: من my.telegram.org
- `TELEGRAM_PHONE_NUMBER`: رقم هاتفك (مع رمز الدولة)
- `TELEGRAM_SESSION`: سيتم إنشاؤه تلقائياً

### 3. تسجيل الدخول بحسابك
```bash
npm run login
```
- سيطلب منك كود التحقق من تليجرام
- سيتم حفظ session للاستخدام المستقبلي
- **مهم:** تأكد من وجود محادثة مع @TrueCaller_Z_Bot قبل التشغيل

### 4. تشغيل المشروع

#### تشغيل الفرونت إند فقط:
```bash
npm run dev
```

#### تشغيل الباك إند فقط:
```bash
npm run server
```

#### تشغيل المشروع كاملاً:
```bash
npm run dev:full
```

## كيفية الحصول على بيانات Telegram

### 1. إنشاء تطبيق Telegram
1. اذهب إلى https://my.telegram.org
2. سجل دخول برقم هاتفك
3. اذهب إلى "API Development Tools"
4. أنشئ تطبيق جديد واحصل على:
   - `API ID`
   - `API Hash`

### 2. إعداد الحساب
1. ضع رقم هاتفك في `TELEGRAM_PHONE_NUMBER`
2. في أول تشغيل، ستحتاج للتحقق من رقمك
3. سيتم حفظ session للاستخدام المستقبلي

### 3. التواصل مع TrueCaller Bot
- النظام سيستخدم حسابك للتواصل مع `@TrueCaller_Z_Bot`
- تأكد من أن لديك محادثة مع البوت مسبقاً

## البناء للإنتاج

```bash
npm run build
```

## الهيكل

```
NUMINF/
├── src/
│   ├── components/
│   │   ├── PhoneInput.jsx
│   │   ├── ResultDisplay.jsx
│   │   └── LoadingSpinner.jsx
│   ├── App.jsx
│   ├── App.css
│   └── main.jsx
├── server/
│   ├── routes/
│   │   └── api.js
│   ├── services/
│   │   └── truecaller.js
│   └── server.js
├── public/
└── package.json
```

## المساهمة

نرحب بالمساهمات! يرجى فتح Issue أو Pull Request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
