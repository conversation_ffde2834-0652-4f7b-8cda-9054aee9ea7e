// إعدادات التطبيق
export const config = {
  // إعدادات الخادم
  server: {
    port: process.env.PORT || 5000,
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? 'https://your-domain.com' 
        : 'http://localhost:5173'
    }
  },

  // إعدادات Telegram
  telegram: {
    apiId: parseInt(process.env.TELEGRAM_API_ID),
    apiHash: process.env.TELEGRAM_API_HASH,
    phoneNumber: process.env.TELEGRAM_PHONE_NUMBER,
    session: process.env.TELEGRAM_SESSION || '',
    botUsername: '@TrueCaller_Z_Bot'
  },

  // إعدادات التطبيق
  app: {
    name: 'NUMINF',
    version: '1.0.0',
    description: 'Number Information Lookup Service'
  },

  // إعدادات البحث
  lookup: {
    timeout: 10000, // 10 ثواني
    retries: 3,
    delay: 2000 // تأخير بين المحاولات
  }
};

export default config;
