const LoadingSpinner = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="relative">
        {/* Outer spinning ring */}
        <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        
        {/* Inner pulsing dot */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-4 h-4 bg-blue-600 rounded-full animate-pulse"></div>
        </div>
      </div>
      
      <div className="mt-6 text-center">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          جاري البحث عن المعلومات...
        </h3>
        <p className="text-sm text-gray-600 max-w-md">
          نحن نتواصل مع خدمة TrueCaller للحصول على معلومات الرقم
        </p>
      </div>

      {/* Progress dots */}
      <div className="flex space-x-1 mt-4 space-x-reverse">
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>

      {/* Estimated time */}
      <div className="mt-4 text-xs text-gray-500">
        الوقت المتوقع: 3-5 ثواني
      </div>
    </div>
  )
}

export default LoadingSpinner
