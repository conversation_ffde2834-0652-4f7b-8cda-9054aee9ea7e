#!/usr/bin/env node

import dotenv from 'dotenv';
import TelegramAuth from './server/auth/telegram-auth.js';

dotenv.config();

async function login() {
  console.log('🚀 NUMINF - تسجيل الدخول إلى تليجرام');
  console.log('=====================================\n');

  // التحقق من وجود البيانات المطلوبة
  if (!process.env.TELEGRAM_API_ID || !process.env.TELEGRAM_API_HASH || !process.env.TELEGRAM_PHONE_NUMBER) {
    console.error('❌ بيانات تليجرام غير مكتملة!');
    console.log('يرجى تشغيل: npm run setup');
    process.exit(1);
  }

  const auth = new TelegramAuth();

  // التحقق من حالة التسجيل الحالية
  const isLoggedIn = await auth.checkAuth();
  
  if (isLoggedIn) {
    console.log('✅ أنت مسجل دخول بالفعل!');
    console.log('يمكنك الآن تشغيل الخادم: npm run server');
    process.exit(0);
  }

  // تسجيل الدخول
  console.log(`📱 سيتم تسجيل الدخول برقم: ${process.env.TELEGRAM_PHONE_NUMBER}`);
  console.log('📨 سيتم إرسال كود التحقق إلى تليجرام...\n');

  const result = await auth.authenticate();

  if (result.success) {
    console.log('\n🎉 تم تسجيل الدخول بنجاح!');
    console.log('📋 الخطوات التالية:');
    console.log('1. تأكد من وجود محادثة مع @TrueCaller_Z_Bot');
    console.log('2. شغل الخادم: npm run server');
    console.log('3. شغل الواجهة: npm run dev');
  } else {
    console.error('❌ فشل في تسجيل الدخول:', result.error);
    process.exit(1);
  }
}

login().catch(console.error);
