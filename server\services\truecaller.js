import axios from 'axios';

class TruecallerService {
  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN;
    this.botUsername = '@TrueCaller_Z_Bot';
    this.telegramApiUrl = 'https://api.telegram.org/bot';
    
    // You'll need to get your chat ID with the bot
    this.chatId = process.env.TELEGRAM_CHAT_ID;
  }

  async lookupNumber(phoneNumber) {
    try {
      // Clean the phone number
      const cleanNumber = phoneNumber.replace(/\D/g, '');
      
      // Format the message to send to the bot
      const message = cleanNumber;

      // Send message to TrueCaller bot
      const response = await this.sendMessageToBot(message);
      
      if (response.success) {
        // Wait a bit for the bot to respond
        await this.delay(3000);
        
        // Get the bot's response
        const botResponse = await this.getBotResponse();
        
        if (botResponse.success) {
          return {
            success: true,
            data: this.parseResponse(botResponse.data),
            rawResponse: botResponse.data
          };
        } else {
          return {
            success: false,
            error: 'Failed to get response from TrueCaller bot'
          };
        }
      } else {
        return {
          success: false,
          error: 'Failed to send request to TrueCaller bot'
        };
      }

    } catch (error) {
      console.error('TrueCaller service error:', error);
      return {
        success: false,
        error: 'Service temporarily unavailable'
      };
    }
  }

  async sendMessageToBot(message) {
    try {
      if (!this.botToken || !this.chatId) {
        throw new Error('Bot token or chat ID not configured');
      }

      const url = `${this.telegramApiUrl}${this.botToken}/sendMessage`;
      const response = await axios.post(url, {
        chat_id: this.chatId,
        text: message,
        parse_mode: 'HTML'
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error sending message to bot:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getBotResponse() {
    try {
      const url = `${this.telegramApiUrl}${this.botToken}/getUpdates`;
      const response = await axios.get(url, {
        params: {
          offset: -1,
          limit: 1
        }
      });

      if (response.data.result && response.data.result.length > 0) {
        const lastMessage = response.data.result[0].message;
        
        return {
          success: true,
          data: lastMessage.text || 'No text response'
        };
      } else {
        return {
          success: false,
          error: 'No response from bot'
        };
      }
    } catch (error) {
      console.error('Error getting bot response:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  parseResponse(responseText) {
    // Parse the TrueCaller bot response
    // This will depend on the format of the bot's response
    try {
      const lines = responseText.split('\n');
      const result = {
        phoneNumber: '',
        name: '',
        location: '',
        carrier: '',
        type: '',
        rawText: responseText
      };

      // Basic parsing - you may need to adjust based on actual bot response format
      lines.forEach(line => {
        const lowerLine = line.toLowerCase();
        if (lowerLine.includes('name:') || lowerLine.includes('الاسم:')) {
          result.name = line.split(':')[1]?.trim() || '';
        } else if (lowerLine.includes('location:') || lowerLine.includes('الموقع:')) {
          result.location = line.split(':')[1]?.trim() || '';
        } else if (lowerLine.includes('carrier:') || lowerLine.includes('الشركة:')) {
          result.carrier = line.split(':')[1]?.trim() || '';
        } else if (lowerLine.includes('type:') || lowerLine.includes('النوع:')) {
          result.type = line.split(':')[1]?.trim() || '';
        }
      });

      return result;
    } catch (error) {
      console.error('Error parsing response:', error);
      return {
        rawText: responseText,
        error: 'Failed to parse response'
      };
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default new TruecallerService();
