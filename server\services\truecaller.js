import TelegramAuth from '../auth/telegram-auth.js';

class TruecallerService {
  constructor() {
    this.botUsername = '@TrueCaller_Z_Bot';
    this.auth = new TelegramAuth();
    this.client = null;
    this.isConnected = false;
  }

  async initializeClient() {
    if (this.isConnected && this.client) return;

    try {
      // التحقق من حالة التسجيل
      const isLoggedIn = await this.auth.checkAuth();

      if (!isLoggedIn) {
        throw new Error('غير مسجل دخول. يرجى تشغيل: npm run login');
      }

      this.client = this.auth.client;
      this.isConnected = true;
      console.log('✅ تم الاتصال بـ Telegram بنجاح');

    } catch (error) {
      console.error('❌ فشل في الاتصال بـ Telegram:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  async lookupNumber(phoneNumber) {
    try {
      // تنظيف رقم الهاتف
      const cleanNumber = phoneNumber.replace(/\D/g, '');

      // التأكد من الاتصال
      await this.initializeClient();

      if (!this.isConnected) {
        return {
          success: false,
          error: 'فشل في الاتصال بحساب تليجرام. تأكد من إعداد البيانات بشكل صحيح.'
        };
      }

      // إرسال الرسالة للبوت والحصول على الرد
      const result = await this.sendMessageToBot(cleanNumber);
      return result;

    } catch (error) {
      console.error('TrueCaller service error:', error);
      return {
        success: false,
        error: 'حدث خطأ في خدمة البحث. يرجى المحاولة مرة أخرى.'
      };
    }
  }

  async sendMessageToBot(phoneNumber) {
    try {
      if (!this.client || !this.isConnected) {
        throw new Error('Telegram client not connected');
      }

      // البحث عن البوت
      const bot = await this.client.getEntity(this.botUsername);

      // إرسال رقم الهاتف
      await this.client.sendMessage(bot, { message: phoneNumber });

      // انتظار الرد (5 ثواني)
      await this.delay(5000);

      // الحصول على آخر رسالة من البوت
      const messages = await this.client.getMessages(bot, { limit: 1 });

      if (messages && messages.length > 0) {
        const response = messages[0].message;

        return {
          success: true,
          data: this.parseResponse(response),
          rawResponse: response
        };
      } else {
        throw new Error('No response from bot');
      }

    } catch (error) {
      console.error('Error communicating with bot:', error);
      return {
        success: false,
        error: 'فشل في التواصل مع بوت TrueCaller. تأكد من وجود محادثة مع البوت.'
      };
    }
  }



  parseResponse(responseText) {
    try {
      const result = {
        phoneNumber: '',
        name: '',
        location: '',
        carrier: '',
        type: '',
        rawText: responseText
      };

      // استخراج المعلومات من النص
      const lines = responseText.split('\n');

      lines.forEach(line => {
        if (line.includes('👤 الاسم:')) {
          result.name = line.replace('👤 الاسم:', '').trim();
        } else if (line.includes('📍 الموقع:')) {
          result.location = line.replace('📍 الموقع:', '').trim();
        } else if (line.includes('📡 الشركة:')) {
          result.carrier = line.replace('📡 الشركة:', '').trim();
        } else if (line.includes('📞 النوع:')) {
          result.type = line.replace('📞 النوع:', '').trim();
        } else if (line.includes('📱 معلومات الرقم:')) {
          result.phoneNumber = line.replace('📱 معلومات الرقم:', '').trim();
        }
      });

      return result;
    } catch (error) {
      console.error('Error parsing response:', error);
      return {
        rawText: responseText,
        error: 'فشل في تحليل الاستجابة'
      };
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default new TruecallerService();
