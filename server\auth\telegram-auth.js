import pkg from 'telegram';
const { TelegramApi } = pkg;
import { StringSession } from 'telegram/sessions/index.js';
import readline from 'readline';

class TelegramAuth {
  constructor() {
    this.apiId = parseInt(process.env.TELEGRAM_API_ID);
    this.apiHash = process.env.TELEGRAM_API_HASH;
    this.phoneNumber = process.env.TELEGRAM_PHONE_NUMBER;
    this.session = new StringSession(process.env.TELEGRAM_SESSION || '');
    this.client = null;
  }

  async authenticate() {
    try {
      console.log('🔐 بدء عملية تسجيل الدخول إلى تليجرام...');
      
      this.client = new TelegramApi(this.session, this.apiId, this.apiHash, {
        connectionRetries: 5,
      });

      await this.client.start({
        phoneNumber: this.phoneNumber,
        password: async () => {
          return await this.askForPassword();
        },
        phoneCode: async () => {
          return await this.askForCode();
        },
        onError: (err) => {
          console.error('❌ خطأ في التسجيل:', err.message);
        },
      });

      // حفظ session للاستخدام المستقبلي
      const sessionString = this.client.session.save();
      this.updateEnvFile(sessionString);

      console.log('✅ تم تسجيل الدخول بنجاح!');
      console.log('📝 تم حفظ session للاستخدام المستقبلي');
      
      return {
        success: true,
        client: this.client,
        session: sessionString
      };

    } catch (error) {
      console.error('❌ فشل في تسجيل الدخول:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async askForCode() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('📱 أدخل كود التحقق المرسل إلى تليجرام: ', (code) => {
        rl.close();
        resolve(code);
      });
    });
  }

  async askForPassword() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('🔒 أدخل كلمة المرور الثنائية (اتركها فارغة إذا لم تكن مفعلة): ', (password) => {
        rl.close();
        resolve(password);
      });
    });
  }

  async updateEnvFile(sessionString) {
    try {
      const fs = await import('fs');
      let envContent = fs.readFileSync('.env', 'utf8');

      // تحديث أو إضافة TELEGRAM_SESSION
      if (envContent.includes('TELEGRAM_SESSION=')) {
        envContent = envContent.replace(
          /TELEGRAM_SESSION=.*/,
          `TELEGRAM_SESSION=${sessionString}`
        );
      } else {
        envContent += `\nTELEGRAM_SESSION=${sessionString}`;
      }

      fs.writeFileSync('.env', envContent);
      console.log('💾 تم تحديث ملف .env');

    } catch (error) {
      console.error('❌ فشل في تحديث ملف .env:', error.message);
    }
  }

  async checkAuth() {
    try {
      if (!this.session.session) {
        return false;
      }

      this.client = new TelegramApi(this.session, this.apiId, this.apiHash);
      await this.client.connect();
      
      const me = await this.client.getMe();
      console.log(`✅ مسجل دخول باسم: ${me.firstName} ${me.lastName || ''}`);
      
      return true;
    } catch (error) {
      console.log('❌ غير مسجل دخول، يجب التسجيل أولاً');
      return false;
    }
  }
}

export default TelegramAuth;
