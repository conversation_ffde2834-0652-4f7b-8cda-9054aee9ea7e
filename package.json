{"name": "numinf", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\""}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "concurrently": "^8.2.2", "nodemon": "^3.0.2"}}