# 🚀 دليل البدء السريع - NUMINF

## الخطوات الأساسية

### 1. التثبيت
```bash
npm install
```

### 2. الإعداد السريع
```bash
npm run setup
```
سيطلب منك:
- API ID (من my.telegram.org)
- API Hash (من my.telegram.org)
- رقم هاتفك (مع رمز الدولة مثل +201234567890)

### 3. تسجيل الدخول بحسابك
```bash
npm run login
```
- سيطلب منك كود التحقق من تليجرام
- سيتم حفظ session للاستخدام المستقبلي

### 4. التشغيل
```bash
# تشغيل الخادم
npm run server

# في terminal آخر - تشغيل الواجهة
npm run dev

# أو تشغيل الاثنين معاً
npm run dev:full
```

### 5. الاستخدام
1. افتح http://localhost:5173
2. أدخل رقم الهاتف
3. اضغط "البحث عن المعلومات"

## ملاحظات مهمة

### 🔐 الأمان
- لا تشارك ملف `.env` مع أحد
- بياناتك الشخصية محمية ولا تُرسل لأي خادم خارجي

### 📱 كيف يعمل النظام
1. النظام يستخدم حسابك الشخصي في تليجرام
2. يرسل رقم الهاتف إلى @TrueCaller_Z_Bot
3. يحصل على الرد ويعرضه لك

### ⚠️ في حالة المشاكل
- تأكد من أن لديك محادثة مع @TrueCaller_Z_Bot في تليجرام
- تأكد من صحة بيانات API من my.telegram.org
- في أول تشغيل قد تحتاج لإدخال كود التحقق

### 🛠️ التطوير
- الكود مفتوح المصدر ويمكن تعديله
- الواجهة: React + Tailwind CSS
- الخادم: Node.js + Express
- التواصل: Telegram Client API

## الدعم
إذا واجهت أي مشكلة، تأكد من:
1. تثبيت Node.js (الإصدار 18 أو أحدث)
2. اتصال إنترنت مستقر
3. صحة بيانات Telegram API
