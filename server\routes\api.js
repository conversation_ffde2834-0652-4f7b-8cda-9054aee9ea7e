import express from 'express';
import truecallerService from '../services/truecaller.js';

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'NUMINF API is running',
    timestamp: new Date().toISOString()
  });
});

// Phone number lookup endpoint
router.post('/lookup', async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    // Validate phone number
    if (!phoneNumber) {
      return res.status(400).json({
        error: 'Phone number is required',
        success: false
      });
    }

    // Basic phone number validation
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    if (cleanNumber.length < 10 || cleanNumber.length > 15) {
      return res.status(400).json({
        error: 'Invalid phone number format',
        success: false
      });
    }

    // Call TrueCaller service
    const result = await truecallerService.lookupNumber(phoneNumber);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Number lookup successful'
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Number not found',
        message: 'Could not retrieve information for this number'
      });
    }

  } catch (error) {
    console.error('Lookup error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to process your request'
    });
  }
});

// Rate limiting info endpoint
router.get('/limits', (req, res) => {
  res.json({
    message: 'API rate limits',
    limits: {
      requests_per_minute: 10,
      requests_per_hour: 100
    }
  });
});

export default router;
